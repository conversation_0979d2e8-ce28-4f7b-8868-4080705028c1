import 'package:intl/intl.dart';

class DonationEvent {
  final String id;
  final String title;
  final String location;
  final String organizationName;
  final String eventDate;
  final String endTime;
  final int requiredDonors;
  final int currentDonors;
  final String description;
  final String? image;
  final String status;
  final bool isEmergency;

  DonationEvent({
    required this.id,
    required this.title,
    required this.location,
    required this.organizationName,
    required this.eventDate,
    required this.endTime,
    required this.requiredDonors,
    required this.currentDonors,
    required this.description,
    this.image,
    required this.status,
    required this.isEmergency,
  });

  factory DonationEvent.fromJson(Map<String, dynamic> json) {
    print('DonationEvent.fromJson - Raw data: $json');

    return DonationEvent(
      id: json['Id']?.toString() ?? json['id']?.toString() ?? '',
      title: json['Title']?.toString() ?? json['title']?.toString() ?? '',
      location: json['Location']?.toString() ?? json['location']?.toString() ?? '',
      organizationName: json['OrganizationName']?.toString() ?? json['organizationName']?.toString() ?? '',
      eventDate: json['EventDate']?.toString() ?? json['eventDate']?.toString() ?? '',
      endTime: json['EndTime']?.toString() ?? json['endTime']?.toString() ?? '',
      requiredDonors: (json['RequiredDonors'] as num?)?.toInt() ??
                      (json['requiredDonors'] as num?)?.toInt() ?? 0,
      currentDonors: (json['CurrentDonors'] as num?)?.toInt() ??
                     (json['currentDonors'] as num?)?.toInt() ?? 0,
      description: json['Description']?.toString() ?? json['description']?.toString() ?? '',
      image: json['Image']?.toString() ?? json['image']?.toString(),
      status: json['Status']?.toString() ?? json['status']?.toString() ?? 'Unknown',
      isEmergency: json['IsEmergency'] as bool? ?? json['isEmergency'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'location': location,
      'organizationName': organizationName,
      'eventDate': eventDate,
      'endTime': endTime,
      'requiredDonors': requiredDonors,
      'currentDonors': currentDonors,
      'description': description,
      'image': image,
      'status': status,
      'isEmergency': isEmergency,
    };
  }

  String getFormattedDate() {
    try {
      final date = DateTime.parse(eventDate);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return eventDate.split('T')[0];
    }
  }

  String getFormattedTime() {
    try {
      final start = DateTime.parse(eventDate);
      final end = DateTime.parse(endTime);
      final timeFormat = DateFormat('HH:mm');
      return '${timeFormat.format(start)} - ${timeFormat.format(end)}';
    } catch (e) {
      String formatTime(String dateStr) {
        if (dateStr.contains('T')) {
          final parts = dateStr.split('T');
          final timePart = parts[1].split('.')[0];
          return timePart;
        } else {
          return dateStr;
        }
      }

      final startTime = formatTime(eventDate);
      final endTimeStr = formatTime(endTime);
      return '$startTime - $endTimeStr';
    }
  }
}