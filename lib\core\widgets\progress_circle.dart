import 'package:bloodplus/core/language_helper/localization.dart';
import 'package:bloodplus/core/widgets/dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;

class ProgressCircle extends StatefulWidget {
  final int daysLeft;
  final String label;
  final String donationType;

  const ProgressCircle({
    Key? key,
    required this.daysLeft,
    required this.label,
    required this.donationType,
  }) : super(key: key);

  @override
  State<ProgressCircle> createState() => _ProgressCircleState();
}

class _ProgressCircleState extends State<ProgressCircle>
    with TickerProviderStateMixin {
  late AnimationController _glowController;
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _sparkleController;
  late AnimationController _breatheController;

  late Animation<double> _glowAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _progressAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _sparkleAnimation;
  late Animation<double> _breatheAnimation;

  // Map to store total days for each donation type
  static const Map<String, int> _totalDaysMap = {
    'wholeblood': 84,
    'redbloodcells': 84,
    'plasma': 14,
    'platelets': 14,
    'whitebloodcells': 7,
  };

  @override
  void initState() {
    super.initState();

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    )..repeat(reverse: true);

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1800),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 6000),
      vsync: this,
    )..repeat();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _sparkleController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    )..repeat();

    _breatheController = AnimationController(
      duration: const Duration(milliseconds: 4000),
      vsync: this,
    )..repeat(reverse: true);

    _glowAnimation = Tween<double>(begin: 0.4, end: 1.0).animate(
      CurvedAnimation(parent: _glowController, curve: Curves.easeInOutSine),
    );

    _pulseAnimation = Tween<double>(begin: 0.98, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOutCubic),
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 2 * math.pi).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _scaleAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _sparkleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _sparkleController, curve: Curves.easeInOutQuart),
    );

    _breatheAnimation = Tween<double>(begin: 0.96, end: 1.02).animate(
      CurvedAnimation(parent: _breatheController, curve: Curves.easeInOutSine),
    );

    // Get total days based on donation type
    final totalDays = _totalDaysMap[widget.donationType.toLowerCase()] ?? 84;

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.daysLeft >= totalDays ? 0.0 : 1.0 - (widget.daysLeft / totalDays),
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOutBack,
    ));

    _scaleController.forward();

    if (widget.daysLeft == 0) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _glowController.dispose();
    _pulseController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    _sparkleController.dispose();
    _breatheController.dispose();
    super.dispose();
  }

  Color get _primaryColor {
    if (widget.daysLeft == 0) return const Color(0xFF10B981); // Emerald
    if (widget.daysLeft <= 7) return const Color(0xFFF59E0B); // Amber
    return const Color(0xFFEF4444); // Red
  }

  List<Color> get _gradientColors {
    if (widget.daysLeft == 0) {
      return [
        const Color(0xFF34D399),
        const Color(0xFF10B981),
        const Color(0xFF059669),
      ];
    }
    if (widget.daysLeft <= 7) {
      return [
        const Color(0xFFFFFD11),
        const Color(0xFFF5FF00),
        const Color(0xFF848D05),
      ];
    }
    return [
      const Color(0xFFF87171),
      const Color(0xFFEF4444),
      const Color(0xFFDC2626),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context);
    final bool canDonate = widget.daysLeft == 0;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _scaleController.reset();
        _scaleController.forward();

        DialogHelper.showAnimatedSuccessDialog(
          context: context,
          title: canDonate
              ? localizations.translate('donation_today_title')
              : localizations.translate('donation_day_count_title'),
          message: canDonate
              ? localizations.translate('donation_today_message')
              : localizations.translate('donation_day_count_message').replaceAll('{days}', widget.daysLeft.toString()),
          buttonText: localizations.translate('ok'),
        );
      },
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _pulseAnimation,
          _scaleAnimation,
          _breatheAnimation
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value *
                (canDonate ? _pulseAnimation.value : _breatheAnimation.value),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.25),
                    Colors.white.withOpacity(0.15),
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.02),
                  ],
                  stops: const [0.0, 0.3, 0.7, 1.0],
                ),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  // Main shadow
                  BoxShadow(
                    color: Colors.black.withOpacity(0.12),
                    blurRadius: 24,
                    offset: const Offset(0, 12),
                    spreadRadius: -4,
                  ),
                  // Inner glow
                  BoxShadow(
                    color: _primaryColor.withOpacity(0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 6),
                    spreadRadius: -8,
                  ),
                  // Subtle top highlight
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                    spreadRadius: -4,
                  ),
                  // Enhanced glow for ready state
                  if (canDonate)
                    BoxShadow(
                      color: const Color(0xFF10B981).withOpacity(0.4),
                      blurRadius: 20,
                      spreadRadius: 4,
                    ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      // Animated outer glow
                      AnimatedBuilder(
                        animation: _glowAnimation,
                        builder: (context, child) {
                          return Container(
                            width: 75,
                            height: 75,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: RadialGradient(
                                colors: [
                                  _primaryColor.withOpacity(0.0),
                                  _primaryColor.withOpacity(0.15 * _glowAnimation.value),
                                  _primaryColor.withOpacity(0.3 * _glowAnimation.value),
                                  _primaryColor.withOpacity(0.1 * _glowAnimation.value),
                                  Colors.transparent,
                                ],
                                stops: const [0.0, 0.3, 0.6, 0.8, 1.0],
                              ),
                            ),
                          );
                        },
                      ),
                      // Sparkle effects for ready state
                      if (canDonate)
                        AnimatedBuilder(
                          animation: _sparkleAnimation,
                          builder: (context, child) {
                            return Stack(
                              children: List.generate(6, (index) {
                                final angle = (index * 60.0) * (math.pi / 180);
                                final radius = 35 + (math.sin(_sparkleAnimation.value * 2 * math.pi) * 5);
                                final x = math.cos(angle + _sparkleAnimation.value * 2 * math.pi) * radius;
                                final y = math.sin(angle + _sparkleAnimation.value * 2 * math.pi) * radius;

                                return Positioned(
                                  left: 42.5 + x,
                                  top: 42.5 + y,
                                  child: Container(
                                    width: 4,
                                    height: 4,
                                    decoration: BoxDecoration(
                                      color: Colors.white.withOpacity(
                                          0.8 * math.sin(_sparkleAnimation.value * math.pi).abs()
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.white.withOpacity(0.5),
                                          blurRadius: 6,
                                          spreadRadius: 1,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                            );
                          },
                        ),
                      // Rotating gradient ring for ready state
                      if (canDonate)
                        AnimatedBuilder(
                          animation: _rotationAnimation,
                          builder: (context, child) {
                            return Transform.rotate(
                              angle: _rotationAnimation.value,
                              child: Container(
                                width: 85,
                                height: 85,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: SweepGradient(
                                    colors: [
                                      Colors.transparent,
                                      _primaryColor.withOpacity(0.3),
                                      _primaryColor.withOpacity(0.7),
                                      _primaryColor.withOpacity(0.4),
                                      Colors.transparent,
                                    ],
                                    stops: const [0.0, 0.2, 0.5, 0.8, 1.0],
                                    transform: const GradientRotation(math.pi / 4),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      // Main progress circle
                      SizedBox(
                        width: 80,
                        height: 80,
                        child: AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return CustomPaint(
                              painter: EnhancedProgressPainter(
                                progress: _progressAnimation.value,
                                colors: _gradientColors,
                                backgroundColor: Colors.white.withOpacity(0.2),
                                glowStrength: _glowAnimation.value,
                                canDonate: canDonate,
                              ),
                            );
                          },
                        ),
                      ),
                      // Center content with glassmorphism effect
                      Container(
                        width: 66,
                        height: 66,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: RadialGradient(
                            colors: [
                              Colors.white.withOpacity(0.3),
                              Colors.white.withOpacity(0.15),
                              Colors.white.withOpacity(0.05),
                            ],
                            stops: const [0.0, 0.6, 1.0],
                          ),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: canDonate
                            ? Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                const Color(0xFF10B981).withOpacity(0.4),
                                const Color(0xFF059669).withOpacity(0.2),
                              ],
                            ),
                          ),
                          child: const Icon(
                            Icons.favorite_rounded,
                            color: Colors.white,
                            size: 28,
                          ),
                        )
                            : Center(
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white.withOpacity(0.25),
                                  Colors.white.withOpacity(0.1),
                                ],
                              ),
                            ),
                            child: Text(
                              '${widget.daysLeft}',
                              style: GoogleFonts.poppins(
                                color: Colors.white,
                                fontSize: 22,
                                fontWeight: FontWeight.w800,
                                letterSpacing: -0.5,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withOpacity(0.4),
                                    offset: const Offset(0, 1),
                                    blurRadius: 3,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 11),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withOpacity(0.25),
                          Colors.white.withOpacity(0.15),
                          Colors.white.withOpacity(0.08),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: AutoSizeText(
                      canDonate
                          ? localizations.translate('donation_today_title')
                          : localizations.translate(widget.label).replaceAll('{days}', widget.daysLeft.toString()),
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        height: 1,
                        letterSpacing: 0.2,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            offset: const Offset(0, 1),
                            blurRadius: 2,
                          ),
                        ],
                      ),
                      maxLines: 2,
                      minFontSize: 10,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class EnhancedProgressPainter extends CustomPainter {
  final double progress;
  final List<Color> colors;
  final Color backgroundColor;
  final double glowStrength;
  final bool canDonate;

  EnhancedProgressPainter({
    required this.progress,
    required this.colors,
    required this.backgroundColor,
    required this.glowStrength,
    required this.canDonate,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;
    const strokeWidth = 9.0;

    // Background circle with enhanced styling
    final backgroundPaint = Paint()
      ..color = backgroundColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius - strokeWidth / 2, backgroundPaint);

    // Inner shadow for depth
    final innerShadowPaint = Paint()
      ..color = Colors.black.withOpacity(0.15)
      ..strokeWidth = strokeWidth - 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius - strokeWidth / 2 - 1.5, innerShadowPaint);

    if (progress > 0 || canDonate) {
      // Enhanced outer glow
      final glowPaint = Paint()
        ..shader = LinearGradient(
          colors: [
            colors[0].withOpacity(0.8 * glowStrength),
            colors[1].withOpacity(0.6 * glowStrength),
            colors[2].withOpacity(0.4 * glowStrength),
          ],
        ).createShader(Rect.fromCircle(center: center, radius: radius))
        ..strokeWidth = strokeWidth + 5
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 6);

      const startAngle = -math.pi / 2;
      final sweepAngle = canDonate ? 2 * math.pi : 2 * math.pi * progress;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        glowPaint,
      );

      // Main progress arc with enhanced gradient
      final progressPaint = Paint()
        ..shader = LinearGradient(
          colors: colors,
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0.0, 0.5, 1.0],
        ).createShader(Rect.fromCircle(center: center, radius: radius))
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
        startAngle,
        sweepAngle,
        false,
        progressPaint,
      );

      // Enhanced highlight at the end
      if (!canDonate && progress > 0) {
        final highlightPaint = Paint()
          ..color = Colors.white.withOpacity(0.9)
          ..strokeWidth = 3
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;

        final endAngle = startAngle + sweepAngle;
        final highlightStart = endAngle - 0.15;
        const highlightSweep = 0.3;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: radius - strokeWidth / 2),
          highlightStart,
          highlightSweep,
          false,
          highlightPaint,
        );
      }

      // Subtle inner highlight for premium look
      final innerHighlightPaint = Paint()
        ..color = Colors.white.withOpacity(0.3)
        ..strokeWidth = 2
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius - strokeWidth / 2 - 2),
        startAngle,
        sweepAngle * 0.3,
        false,
        innerHighlightPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}