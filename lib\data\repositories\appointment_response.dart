import 'package:bloodplus/data/models/appointment_model.dart';

class AppointmentResponse {
  final List<Appointment> items;
  final int totalItems;
  final int totalPages;
  final int currentPage;
  final int pageSize;
  final bool hasPreviousPage;
  final bool hasNextPage;

  AppointmentResponse({
    required this.items,
    required this.totalItems,
    required this.totalPages,
    required this.currentPage,
    required this.pageSize,
    required this.hasPreviousPage,
    required this.hasNextPage,
  });

  factory AppointmentResponse.fromJson(Map<String, dynamic> json) {
    // API trả về data trong key "Message" với PascalCase
    final message = json['Message'] as Map<String, dynamic>? ??
                   json['message'] as Map<String, dynamic>? ?? {};

    print('AppointmentResponse.fromJson - message: $message');

    List<dynamic>? itemsData;

    // Kiểm tra cả PascalCase và camelCase
    if (message['Items'] is List) {
      itemsData = message['Items'] as List<dynamic>?;
      print('Items is List: $itemsData');
    } else if (message['items'] is List) {
      itemsData = message['items'] as List<dynamic>?;
      print('items is List: $itemsData');
    } else if (message['Items'] is Map) {
      final itemsMap = message['Items'] as Map<String, dynamic>? ?? {};
      itemsData = itemsMap[r'$values'] as List<dynamic>?;
      print('Items is Map, key \$values: $itemsData');
    } else if (message['items'] is Map) {
      final itemsMap = message['items'] as Map<String, dynamic>? ?? {};
      itemsData = itemsMap[r'$values'] as List<dynamic>?;
      print('items is Map, key \$values: $itemsData');
    }

    final itemsList = itemsData?.map((item) => Appointment.fromJson(item as Map<String, dynamic>)).toList() ?? [];
    print('AppointmentResponse - Final itemsList count: ${itemsList.length}');

    return AppointmentResponse(
      items: itemsList,
      totalItems: (message['TotalItems'] as num?)?.toInt() ??
                  (message['totalItems'] as num?)?.toInt() ?? 0,
      totalPages: (message['TotalPages'] as num?)?.toInt() ??
                  (message['totalPages'] as num?)?.toInt() ?? 0,
      currentPage: (message['CurrentPage'] as num?)?.toInt() ??
                   (message['currentPage'] as num?)?.toInt() ?? 1,
      pageSize: (message['PageSize'] as num?)?.toInt() ??
                (message['pageSize'] as num?)?.toInt() ?? 5,
      hasPreviousPage: message['HasPreviousPage'] as bool? ??
                       message['hasPreviousPage'] as bool? ?? false,
      hasNextPage: message['HasNextPage'] as bool? ??
                   message['hasNextPage'] as bool? ?? false,
    );
  }
}