import 'dart:convert';
import 'dart:io';
import 'package:bloodplus/core/config/api_config.dart';
import 'package:bloodplus/data/manager/user_manager.dart';
import 'package:bloodplus/data/models/donation_event_model.dart';
import 'package:bloodplus/data/repositories/donation_event_response.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

class DonationEventService {
  static final HttpClient _httpClient = HttpClient()
    ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  static final client = IOClient(_httpClient);
  final UserManager _userManager = UserManager();

  Future<DonationEventResponse> getDonationEvents({
    String? location,
    String? startDate,
    String? endDate,
    String? organization,
    int pageNumber = 1,
    int pageSize = 5,
  }) async {
    final token = await _userManager.getUserToken();
    final queryParameters = {
      'location': location,
      'startDate': startDate,
      'endDate': endDate,
      'organization': organization,
      'pageNumber': pageNumber.toString(),
      'pageSize': pageSize.toString(),
    };

    queryParameters.removeWhere((key, value) => value == null);

    final url = Uri.parse(ApiConfig.getFullUrl('/donationevent')).replace(queryParameters: queryParameters);

    try {
      print('=== GET DONATION EVENTS REQUEST ===');
      print('URL: $url');
      print('Token: ${token != null ? 'exists' : 'null'}');

      final response = await client.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'accept': '*/*',
          'Authorization': 'Bearer $token',
        },
      );

      print('=== GET DONATION EVENTS RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return DonationEventResponse.fromJson(data);
      } else {
        throw Exception('Lấy danh sách sự kiện thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('DonationEventService error: $e');
      throw Exception('Lỗi kết nối: $e');
    }
  }

  Future<DonationEvent> getDonationEventById(String id) async {
    final token = await _userManager.getUserToken();
    final url = Uri.parse(ApiConfig.getFullUrl('/donationevent/$id'));

    try {
      final response = await client.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'accept': '*/*',
          'Authorization': 'Bearer $token',
        },
      );

      print('DonationEventById response status: ${response.statusCode}');
      print('DonationEventById response body: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final message = data['Message'] ?? data['message'];
        return DonationEvent.fromJson(message);
      } else {
        throw Exception('Lấy chi tiết sự kiện thất bại: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      throw Exception('Lỗi kết nối: $e');
    }
  }
}