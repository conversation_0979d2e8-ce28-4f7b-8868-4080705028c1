name: bloodplus
description: "Blood donation scheduling app project using Flutter"


version: 1.0.0+1

environment:
  sdk: ^3.7.2


dependencies:
  flutter:
    sdk: flutter
  geolocator: ^9.0.2
  google_maps_flutter: ^2.5.0
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0
  cupertino_icons: ^1.0.8
  carousel_slider: ^5.0.0
  auto_size_text: ^3.0.0
  google_fonts: ^4.0.3
  qr_flutter: ^4.0.0
  screen_brightness: ^0.2.2
  flutter_launcher_icons: ^0.14.3
  shared_preferences: ^2.3.0
  provider: ^6.1.5
  url_launcher: ^6.3.0
  http: ^1.2.2
  google_sign_in: ^6.1.5
  http_parser: ^4.0.2
  flutter_datetime_picker_plus: ^2.2.0
  connectivity_plus: ^6.0.5
  image_picker: ^1.0.4
  permission_handler: ^11.0.1
  path_provider: ^2.1.1

dev_dependencies:
  flutter_test:
    sdk: flutter


  flutter_lints: ^5.0.0


flutter:


  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/locales/

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.png"

