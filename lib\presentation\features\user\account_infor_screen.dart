import 'package:bloodplus/core/language_helper/localization.dart';
import 'package:bloodplus/core/widgets/dialog_helper.dart';
import 'package:bloodplus/data/manager/app_state_notifier.dart';
import 'package:bloodplus/data/manager/user_manager.dart';
import 'package:bloodplus/data/models/user_model.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:bloodplus/core/constants/app_colors.dart';
import 'package:bloodplus/core/widgets/custom_button.dart';
import 'package:provider/provider.dart';

class AccountInfoScreen extends StatefulWidget {
  const AccountInfoScreen({Key? key}) : super(key: key);

  @override
  State<AccountInfoScreen> createState() => _AccountInfoScreenState();
}

class _AccountInfoScreenState extends State<AccountInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isBloodCardHovered = false;
  final UserManager _userManager = UserManager();
  TextEditingController emailController = TextEditingController();
  TextEditingController dobController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Khởi tạo dữ liệu ban đầu từ AppStateNotifier
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final appState = Provider.of<AppStateNotifier>(context, listen: false);
      if (appState.user != null) {
        emailController.text = appState.user!.email;
        dobController.text = appState.user!.dateOfBirth?.toString().split(' ')[0] ?? '';
        phoneNumberController.text = appState.user!.phoneNumber ?? '';
      } else {
        appState.fetchUserProfile(); // Lấy dữ liệu nếu chưa có
      }
    });
  }

  @override
  void dispose() {
    emailController.dispose();
    dobController.dispose();
    phoneNumberController.dispose();
    super.dispose();
  }

  Future<void> _saveUserInfo() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final appState = Provider.of<AppStateNotifier>(context, listen: false);
    try {
      await appState.updateUserInfo(
        email: emailController.text,
        phoneNumber: phoneNumberController.text,
        dateOfBirth: dobController.text,
      );
      if (context.mounted) {
        DialogHelper.showAnimatedSuccessDialog(
          context: context,
          title: AppLocalizations.of(context).translate('sign_up_successful'),
          message: AppLocalizations.of(context).translate('info_saved_successfully'),
          buttonText: AppLocalizations.of(context).translate('close'),
          icon: Icons.check_circle_outline_rounded,
          iconColor: Colors.green,
          onPressed: () => Navigator.pop(context),
        );
      }
    } catch (e) {
      if (context.mounted) {
        DialogHelper.showAnimatedErrorDialog(
          context: context,
          title: AppLocalizations.of(context).translate('error'),
          message: '${AppLocalizations.of(context).translate('error_saving_info')}: $e',
          buttonText: AppLocalizations.of(context).translate('ok'),
          icon: Icons.error_outline_rounded,
          iconColor: AppColors.primaryRed,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: AppColors.primaryRed,
        elevation: 0,
        title: Text(
          localizations.translate('update_info'),
          style: GoogleFonts.poppins(
              color: Colors.white, fontWeight: FontWeight.w600, fontSize: 20),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white, size: 24),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Consumer<AppStateNotifier>(
        builder: (context, appState, child) {
          if (appState.isLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (appState.user == null) {
            return const Center(child: Text('Không tìm thấy thông tin người dùng'));
          }
          final user = appState.user!;
          // Cập nhật controllers khi user thay đổi
          emailController.text = user.email;
          dobController.text = user.dateOfBirth?.toString().split(' ')[0] ?? '';
          phoneNumberController.text = user.phoneNumber ?? '';

          return SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildUserProfileCard(
                  name: user.name,
                  address: user.address ?? 'Không có địa chỉ',
                  bloodType: user.bloodType ?? 'Không xác định',
                  userImage: user.userImage ?? 'assets/images/profile.jpg',
                ),
                const SizedBox(height: 30),
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16)),
                  color: Colors.white,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _sectionTitle(localizations.translate('personal_info')),
                          const SizedBox(height: 16),
                          _buildInputField(localizations.translate('email'),
                              emailController, Icons.email),
                          _buildInputField(localizations.translate('date_of_birth'),
                              dobController, Icons.cake),
                          _buildInputField(localizations.translate('phone_number'),
                              phoneNumberController, Icons.phone),
                          _buildReadOnlyField(localizations.translate('job'),
                              user.job ?? 'Không có nghề nghiệp', Icons.work),
                          _buildReadOnlyField(
                              localizations.translate('donation_count'),
                              user.donationCount.toString(),
                              Icons.favorite),
                          _buildReadOnlyField(
                              localizations.translate('passport_number'),
                              user.passportNumber ?? 'Không có số hộ chiếu',
                              Icons.book),
                          _buildReadOnlyField(
                              localizations.translate('gender'),
                              _getGenderString(user.gender, localizations),
                              Icons.person),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 40),
                Center(
                  child: CustomButton(
                    text: localizations.translate('save_info'),
                    color: AppColors.primaryRed,
                    textColor: Colors.white,
                    onPressed: _saveUserInfo,
                    padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 14),
                    borderRadius: 12,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getGenderString(int? gender, AppLocalizations localizations) {
    if (gender == null) return 'N/A';
    switch (gender) {
      case 0:
        return localizations.translate('male');
      case 1:
        return localizations.translate('female');
      case 2:
        return localizations.translate('other');
      default:
        return 'N/A';
    }
  }

  Widget _buildUserProfileCard({
    required String name,
    required String address,
    required String bloodType,
    required String userImage,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.red[300]!, Colors.yellow[50]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue[800]!.withOpacity(0.3),
            spreadRadius: 3,
            blurRadius: 12,
            offset: const Offset(0, 5),
          ),
        ],
        borderRadius: BorderRadius.circular(30),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.red[200]!, Colors.red[400]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(80),
            ),
            child: ClipOval(
              child: FadeInImage(
                placeholder: const AssetImage('assets/images/profile.jpg'),
                image: userImage.startsWith('http') || userImage.startsWith('https')
                    ? NetworkImage(userImage)
                    : const AssetImage('assets/images/profile.jpg') as ImageProvider,
                width: 130,
                height: 130,
                fit: BoxFit.cover,
                imageErrorBuilder: (context, error, stackTrace) {
                  print('Image load error for $userImage: $error');
                  return Image.asset('assets/images/profile.jpg', width: 130, height: 130, fit: BoxFit.cover);
                },
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            name,
            style: GoogleFonts.poppins(
                fontSize: 27, fontWeight: FontWeight.bold, color: Colors.black38),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            address,
            style: GoogleFonts.poppins(fontSize: 19, color: Colors.redAccent[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          GestureDetector(
            onTap: () => _showBloodGroupDialog(context, bloodType),
            onTapDown: (_) => setState(() => _isBloodCardHovered = true),
            onTapUp: (_) => setState(() => _isBloodCardHovered = false),
            onTapCancel: () => setState(() => _isBloodCardHovered = false),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _isBloodCardHovered
                      ? [Colors.red[700]!, Colors.red[400]!]
                      : [Colors.red[300]!, Colors.red[100]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                border: Border.all(color: Colors.white, width: 2),
                borderRadius: BorderRadius.circular(30),
                boxShadow: _isBloodCardHovered
                    ? [
                  BoxShadow(
                    color: Colors.purple[700]!.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
                    : [],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.bloodtype,
                      color: _isBloodCardHovered ? Colors.white : Colors.red[700],
                      size: 35),
                  const SizedBox(width: 8),
                  Text(
                    bloodType,
                    style: GoogleFonts.poppins(
                      color: _isBloodCardHovered ? Colors.white : Colors.white,
                      fontWeight: FontWeight.w700,
                      fontSize: 25,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _sectionTitle(String title) {
    return Text(
      title,
      style: GoogleFonts.poppins(
          fontSize: 20, fontWeight: FontWeight.w600, color: AppColors.primaryRed),
    );
  }

  Widget _buildInputField(
      String label, TextEditingController controller, IconData icon) {
    final localizations = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: TextFormField(
        controller: controller,
        style: GoogleFonts.poppins(
            fontSize: 16, fontWeight: FontWeight.w500, color: Colors.black87),
        decoration: InputDecoration(
          prefixIcon: Icon(icon, color: AppColors.primaryRed, size: 20),
          labelText: label,
          labelStyle: GoogleFonts.poppins(fontSize: 16, color: Colors.grey[600]),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey[300]!, width: 1.5),
            borderRadius: BorderRadius.circular(12),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: AppColors.primaryRed, width: 2),
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Colors.grey[50],
          contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
        ),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return localizations
                .translate('please_enter')
                .replaceAll('{field}', label.toLowerCase());
          }
          if (label == localizations.translate('phone_number')) {
            if (!RegExp(r'^\+?\d{10,15}$').hasMatch(value)) {
              return localizations.translate('invalid_phone_number');
            }
          }
          return null;
        },
      ),
    );
  }

  Widget _buildReadOnlyField(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: TextFormField(
        initialValue: value,
        readOnly: true,
        style: GoogleFonts.poppins(
            fontSize: 16, fontWeight: FontWeight.w500, color: Colors.black87),
        decoration: InputDecoration(
          prefixIcon: Icon(icon, color: AppColors.primaryRed, size: 20),
          labelText: label,
          labelStyle: GoogleFonts.poppins(fontSize: 16, color: Colors.grey[600]),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.grey[300]!, width: 1.5),
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: Colors.grey[50],
          contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 14),
        ),
      ),
    );
  }

  void _showBloodGroupDialog(BuildContext context, String currentBloodType) {
    final localizations = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          elevation: 8,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  localizations.translate('choose_the_blood_group'),
                  style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryRed),
                ),
                const SizedBox(height: 20),
                Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  alignment: WrapAlignment.center,
                  children: [
                    _buildBloodOption('A+', currentBloodType),
                    _buildBloodOption('A-', currentBloodType),
                    _buildBloodOption('B+', currentBloodType),
                    _buildBloodOption('B-', currentBloodType),
                    _buildBloodOption('AB+', currentBloodType),
                    _buildBloodOption('AB-', currentBloodType),
                    _buildBloodOption('O+', currentBloodType),
                    _buildBloodOption('O-', currentBloodType),
                  ],
                ),
                const SizedBox(height: 20),
                CustomButton(
                  text: localizations.translate('close'),
                  color: AppColors.primaryRed,
                  textColor: Colors.white,
                  onPressed: () => Navigator.pop(context),
                  padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                  borderRadius: 12,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBloodOption(String bloodType, String currentBloodType) {
    bool isSelected = currentBloodType == bloodType;
    return GestureDetector(
      onTap: () async {
        try {
          final appState = Provider.of<AppStateNotifier>(context, listen: false);
          final userId = await _userManager.getUserId();
          final token = await _userManager.getUserToken();
          if (userId != null && token != null) {
            // Cập nhật nhóm máu
            final updatedUser = await appState.updateUserInfo(
              email: emailController.text,
              phoneNumber: phoneNumberController.text,
              dateOfBirth: dobController.text,
              bloodType: bloodType, // Thêm tham số nhóm máu
            );
            setState(() {
              // Cập nhật giao diện nếu cần
            });
            Navigator.pop(context);
          }
        } catch (e) {
          if (context.mounted) {
            DialogHelper.showAnimatedErrorDialog(
              context: context,
              title: AppLocalizations.of(context).translate('error'),
              message: '${AppLocalizations.of(context).translate('error_updating_blood_type')}: $e',
              buttonText: AppLocalizations.of(context).translate('ok'),
              icon: Icons.error_outline_rounded,
              iconColor: AppColors.primaryRed,
            );
          }
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 70,
        height: 70,
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(colors: [AppColors.primaryRed, AppColors.darkRed])
              : LinearGradient(colors: [Colors.grey[200]!, Colors.grey[300]!]),
          borderRadius: BorderRadius.circular(12),
          boxShadow: isSelected
              ? [
            BoxShadow(
              color: AppColors.primaryRed.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ]
              : [],
        ),
        child: Center(
          child: Text(
            bloodType,
            style: GoogleFonts.poppins(
              color: isSelected ? Colors.white : Colors.black87,
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
          ),
        ),
      ),
    );
  }
}