import 'dart:convert';
import 'dart:io';
import 'package:bloodplus/core/config/api_config.dart';
import 'package:bloodplus/data/models/user_model.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';

class UserService {
  static final HttpClient _httpClient = HttpClient()
    ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  static final IOClient _client = IOClient(_httpClient);

  Future<UserModel> getUserInfo(String userId, String token, {bool forceRefresh = false}) async {
    final url = Uri.parse('${ApiConfig.apiUrl}/user/$userId');

    try {
      final headers = {
        'accept': 'application/json',
        'Authorization': 'Bearer $token',
        if (forceRefresh) 'Cache-Control': 'no-cache',
        if (forceRefresh) 'Pragma': 'no-cache',
      };

      print('=== GET USER INFO REQUEST ===');
      print('URL: $url');
      print('Headers: $headers');

      final response = await _client.get(url, headers: headers);

      print('=== GET USER INFO RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return UserModel.fromJson(jsonData);
      } else {
        throw Exception('Lấy thông tin người dùng thất bại: ${response.statusCode}');
      }
    } catch (e) {
      print('Get user info error: $e');
      throw Exception('Lỗi kết nối khi lấy thông tin người dùng: $e');
    }
  }

  Future<Map<String, int>> getDaysWaiting(String userId, String token) async {
    final url = Uri.parse('${ApiConfig.apiUrl}/user/day-watting?id=$userId');

    try {
      final response = await _client.get(
        url,
        headers: {
          'accept': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('=== GET DAYS WAITING RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        // API trả về data trong key "Message" với PascalCase
        final messageData = jsonData['Message'] ?? jsonData['message'] ?? {};

        print('UserService.getDaysWaiting - messageData: $messageData');

        return {
          'wholeBloodDaysLeft': (messageData['WholeBloodDaysLeft'] as num?)?.toInt() ??
                               (messageData['wholeBloodDaysLeft'] as num?)?.toInt() ?? 0,
          'redBloodCellsDaysLeft': (messageData['RedBloodCellsDaysLeft'] as num?)?.toInt() ??
                                  (messageData['redBloodCellsDaysLeft'] as num?)?.toInt() ?? 0,
          'plasmaDaysLeft': (messageData['PlasmaDaysLeft'] as num?)?.toInt() ??
                           (messageData['plasmaDaysLeft'] as num?)?.toInt() ?? 0,
          'plateletsDaysLeft': (messageData['PlateletsDaysLeft'] as num?)?.toInt() ??
                              (messageData['plateletsDaysLeft'] as num?)?.toInt() ?? 0,
          'whiteBloodCellsDaysLeft': (messageData['WhiteBloodCellsDaysLeft'] as num?)?.toInt() ??
                                    (messageData['whiteBloodCellsDaysLeft'] as num?)?.toInt() ?? 0,
        };
      } else {
        throw Exception('Lấy dữ liệu ngày chờ thất bại: ${response.statusCode}');
      }
    } catch (e) {
      print('Get days waiting error: $e');
      throw Exception('Lỗi kết nối khi lấy dữ liệu ngày chờ: $e');
    }
  }

  Future<UserModel> updateUserInfo(
      String userId,
      String token, {
        required String email,
        required String phoneNumber,
        required String dateOfBirth,
        String? bloodType,
      }) async {
    final url = Uri.parse(ApiConfig.getFullUrl(ApiConfig.userUpdate));

    try {
      final response = await _client.put(
        url,
        headers: {
          'accept': 'application/json',
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'email': email,
          'phoneNumber': phoneNumber,
          'dateOfBirth': dateOfBirth,
          if (bloodType != null) 'bloodType': bloodType,
        }),
      );

      print('=== UPDATE USER INFO RESPONSE ===');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return UserModel.fromJson(jsonData);
      } else {
        throw Exception('Cập nhật thông tin người dùng thất bại: ${response.statusCode}');
      }
    } catch (e) {
      print('Update user info error: $e');
      throw Exception('Lỗi kết nối khi cập nhật thông tin người dùng: $e');
    }
  }
}