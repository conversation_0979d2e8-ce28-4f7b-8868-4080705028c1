import 'dart:io';
import 'package:flutter/foundation.dart';

/// Enum để xác định môi trường triển khai
enum Environment {
  development,
  staging,
  production,
}

/// Enum để xác định loại thiết bị
enum DeviceType {
  androidEmulator,
  androidReal,
  ios,
  web,
}

class ApiConfig {
  // ===== CẤU HÌNH CHÍNH =====
  // Để chuyển đổi môi trường, thay đổi giá trị _currentEnvironment:
  // - Environment.development: Sử dụng local server (**************:7026)
  // - Environment.production: Sử dụng Azure production server
  // - Environment.staging: Sử dụng staging server (hiện tại dùng local)
  static const String _localWifiIp = '**************';
  static const String _apiPort = '7026';
  static const String _frontendPort = '3000';
  static const Environment _currentEnvironment = Environment.development;

  // ===== URL CƠ SỞ =====
  static const String _androidEmulatorBaseUrl = 'https://********:$_apiPort';
  static const String _realDeviceBaseUrl = 'https://$_localWifiIp:$_apiPort';
  static const String _webBaseUrl = 'https://localhost:$_apiPort';
  static const String _productionBaseUrl = 'https://bloodplus-a4a5exeadjd9anfa.southeastasia-01.azurewebsites.net';

  // ===== PHƯƠNG THỨC CHÍNH =====
  /// Lấy base URL dựa trên môi trường và loại thiết bị
  static String get baseUrl {
    switch (_currentEnvironment) {
      case Environment.production:
        return _productionBaseUrl;
      case Environment.staging:
        return _realDeviceBaseUrl;
      case Environment.development:
      default:
        return _getDevBaseUrl();
    }
  }

  /// URL cho API (thêm /api vào baseUrl)
  static String get apiUrl => '$baseUrl/api';

  /// URL cho frontend
  static String get frontendUrl => _currentEnvironment == Environment.production
      ? 'https://bloodplus-a4a5exeadjd9anfa.southeastasia-01.azurewebsites.net' // Production frontend URL
      : 'http://$_localWifiIp:$_frontendPort';

  // ===== PHƯƠNG THỨC HỖ TRỢ =====
  /// Xác định loại thiết bị hiện tại
  static DeviceType get currentDeviceType {
    if (kIsWeb) return DeviceType.web;
    if (Platform.isAndroid) {
      return _isAndroidEmulator() ? DeviceType.androidEmulator : DeviceType.androidReal;
    }
    if (Platform.isIOS) {
      return DeviceType.ios;
    }
    throw Exception('Unsupported platform');
  }

  /// Lấy base URL cho môi trường phát triển
  static String _getDevBaseUrl() {
    switch (currentDeviceType) {
      case DeviceType.androidEmulator:
        return _androidEmulatorBaseUrl;
      case DeviceType.androidReal:
        return _realDeviceBaseUrl;
      case DeviceType.web:
        return _webBaseUrl;
      case DeviceType.ios:
        return _realDeviceBaseUrl; // iOS sử dụng cùng URL với thiết bị thật
    }
  }

  /// Kiểm tra xem có đang chạy trên Android Emulator hay không
  static bool _isAndroidEmulator() {
    return Platform.isAndroid &&
        (Platform.environment['ANDROID_EMULATOR'] == 'true' ||
            Platform.environment.containsKey('ANDROID_AVD_HOME'));
  }

  // ===== ENDPOINT CONSTANTS =====
  static const String authLogin = '/auth/auth-account';
  static const String authGoogleLogin = '/auth/login-google';
  static const String authRegister = '/auth/create-account';
  static const String userProfile = '/user/profile';
  static const String userUpdate = '/user/update';
  static const String bloodDonations = '/blood-donations';
  static const String bloodRequests = '/blood-requests';
  static const String appointment = '/appointment';
  static const String blog = '/blog';
  static const String donationEvent = '/donationevent';

  // ===== UTILITY METHODS =====
  /// Tạo URL đầy đủ từ endpoint
  static String getFullUrl(String endpoint) {
    if (endpoint.isEmpty || endpoint == '/') {
      return apiUrl;
    }
    // Đảm bảo endpoint bắt đầu bằng '/' và không có '/' thừa
    final cleanEndpoint = endpoint.startsWith('/') ? endpoint : '/$endpoint';
    return '$apiUrl$cleanEndpoint';
  }

  /// Kiểm tra xem có đang ở môi trường production không
  static bool get isProduction => _currentEnvironment == Environment.production;

  /// Kiểm tra xem có đang ở môi trường development không
  static bool get isDevelopment => _currentEnvironment == Environment.development;

  /// Lấy tên môi trường hiện tại
  static String get environmentName => _currentEnvironment.name;

  /// In thông tin cấu hình hiện tại
  static void printCurrentConfig() {
    print('=== API Configuration ===');
    print('Environment: $_currentEnvironment');
    print('Device Type: $currentDeviceType');
    print('Base URL: $baseUrl');
    print('API URL: $apiUrl');
    print('Frontend URL: $frontendUrl');
    print('========================');
  }

  /// Kiểm tra kết nối đến server
  static Future<bool> checkServerConnection() async {
    try {
      final client = HttpClient()
        ..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      final request = await client.getUrl(Uri.parse(baseUrl));
      final response = await request.close();
      return response.statusCode == 200;
    } catch (e) {
      print('Kiểm tra kết nối server thất bại: $e');
      return false;
    }
  }
}

extension ApiConfigExtension on String {
  String get fullApiUrl => ApiConfig.getFullUrl(this);
}