{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\CODE\\EXE202\\BloodPlusFE\\android\\app\\.cxx\\Debug\\2zp192jo\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\CODE\\EXE202\\BloodPlusFE\\android\\app\\.cxx\\Debug\\2zp192jo\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}