import 'dart:io';
import 'package:bloodplus/data/manager/days_waiting_manager.dart';
import 'package:bloodplus/data/manager/user_manager.dart';
import 'package:bloodplus/data/models/appointment_model.dart';
import 'package:bloodplus/data/models/blog_model.dart';
import 'package:bloodplus/data/models/donation_event_model.dart';
import 'package:bloodplus/data/models/user_model.dart';
import 'package:bloodplus/data/services/appointment_service.dart';
import 'package:bloodplus/data/services/blog_service.dart';
import 'package:bloodplus/data/services/donation_event_service.dart';
import 'package:bloodplus/data/services/user_service.dart';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class AppStateNotifier with ChangeNotifier {
  final DaysWaitingManager _daysWaitingManager = DaysWaitingManager();
  final AppointmentService _appointmentService = AppointmentService();
  final BlogService _blogService = BlogService();
  final DonationEventService _donationEventService = DonationEventService();
  final UserService _userService = UserService();
  final UserManager _userManager = UserManager();

  UserModel? _user;
  UserModel? get user => _user;
  int _donationCount = 0;
  int get donationCount => _donationCount;
  Map<String, int>? _daysWaiting;
  List<Appointment> _appointments = [];
  List<BlogModel> _blogs = [];
  List<DonationEvent> _donationEvents = [];
  bool _isLoading = false;

  Map<String, int>? get daysWaiting => _daysWaiting;
  List<Appointment> get appointments => _appointments;
  List<BlogModel> get blogs => _blogs;
  List<DonationEvent> get donationEvents => _donationEvents;
  bool get isLoading => _isLoading;

  // Reset state when user logs out or switches account
  void resetState() {
    _user = null;
    _donationCount = 0;
    _daysWaiting = null;
    _appointments = [];
    _blogs = [];
    _donationEvents = [];
    _isLoading = false;
    notifyListeners();
  }

  Future<bool> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<void> fetchDaysWaiting(String userId, String token) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchDaysWaiting');
      return;
    }

    print('AppStateNotifier: Starting fetchDaysWaiting for userId: $userId');
    _isLoading = true;
    notifyListeners();

    try {
      _daysWaiting = await _daysWaitingManager.forceRefreshDaysWaiting(userId, token);
      print('AppStateNotifier: Successfully loaded days waiting: $_daysWaiting');
    } catch (e) {
      print('AppStateNotifier: Error in fetchDaysWaiting: $e');
      // Handle error silently
    }

    _isLoading = false;
    notifyListeners();
    print('AppStateNotifier: fetchDaysWaiting completed');
  }

  Future<void> fetchBlogs({bool forceRefresh = false}) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchBlogs');
      return;
    }

    print('AppStateNotifier: Starting fetchBlogs');

    try {
      final response = await _blogService.getBlogs(pageNumber: 1, pageSize: 10);
      _blogs = response.items;
      print('AppStateNotifier: Successfully loaded ${_blogs.length} blogs');
      notifyListeners();
    } catch (e) {
      print('AppStateNotifier: Error in fetchBlogs: $e');
      // Handle error silently
    }

    print('AppStateNotifier: fetchBlogs completed');
  }

  Future<void> fetchDonationEvents({bool forceRefresh = false}) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchDonationEvents');
      return;
    }

    print('AppStateNotifier: Starting fetchDonationEvents');

    try {
      final response = await _donationEventService.getDonationEvents(pageNumber: 1, pageSize: 10);
      _donationEvents = response.items;
      print('AppStateNotifier: Successfully loaded ${_donationEvents.length} donation events');
      notifyListeners();
    } catch (e) {
      print('AppStateNotifier: Error in fetchDonationEvents: $e');
      // Handle error silently
    }

    print('AppStateNotifier: fetchDonationEvents completed');
  }

  Future<void> fetchAllData({bool forceRefresh = false}) async {
    print('AppStateNotifier: Starting fetchAllData');

    final userId = await _userManager.getUserId();
    final token = await _userManager.getUserToken();

    // Fetch user profile first
    await fetchUserProfile(forceRefresh: forceRefresh);

    // Then fetch other data in parallel
    await Future.wait([
      if (userId != null && token != null) fetchDaysWaiting(userId, token),
      if (userId != null && token != null) fetchAppointments(),
      fetchBlogs(forceRefresh: forceRefresh),
      fetchDonationEvents(forceRefresh: forceRefresh),
    ]);

    print('AppStateNotifier: fetchAllData completed');
  }

  Future<void> fetchAppointments() async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchAppointments');
      return;
    }

    print('AppStateNotifier: Starting fetchAppointments');

    try {
      _appointments = await _appointmentService.getAppointments() ?? [];
      print('AppStateNotifier: Successfully loaded ${_appointments.length} appointments');
      notifyListeners();
    } catch (e) {
      print('AppStateNotifier: Error in fetchAppointments: $e');
      _appointments = [];
      notifyListeners();
    }

    print('AppStateNotifier: fetchAppointments completed');
  }

  Future<void> fetchUserProfile({bool forceRefresh = true}) async {
    if (!await _checkConnectivity()) {
      print('AppStateNotifier: No connectivity, skipping fetchUserProfile');
      return;
    }

    print('AppStateNotifier: Starting fetchUserProfile (forceRefresh: $forceRefresh)');
    _isLoading = true;
    notifyListeners();

    try {
      final userId = await _userManager.getUserId();
      final token = await _userManager.getUserToken();

      print('AppStateNotifier: userId=$userId, token=${token != null ? 'exists' : 'null'}');

      if (userId != null && token != null) {
        // Thêm tham số để đảm bảo không lấy từ cache
        _user = await _userService.getUserInfo(userId, token, forceRefresh: forceRefresh);
        await _userManager.updateUserProfile(_user!);
        _donationCount = _user!.donationCount ?? 0;

        print('AppStateNotifier: Successfully loaded user profile:');
        print('  - ID: ${_user!.id}');
        print('  - Name: "${_user!.name}"');
        print('  - Email: ${_user!.email}');
        print('  - UserImage: ${_user!.userImage}');
        print('  - Donation count: $_donationCount');
        print('  - Address: ${_user!.address}');
        print('  - BloodType: ${_user!.bloodType}');
      } else {
        print('AppStateNotifier: Missing userId or token');
      }
    } catch (e) {
      print('AppStateNotifier: Error in fetchUserProfile: $e');
      // Không throw error để không crash app
    }

    _isLoading = false;
    notifyListeners();
    print('AppStateNotifier: fetchUserProfile completed');
  }

  Future<void> updateUserInfo({
    required String email,
    required String phoneNumber,
    required String dateOfBirth,
    String? bloodType, // Thêm tham số bloodType
  }) async {
    if (!await _checkConnectivity()) {
      return;
    }

    _isLoading = true;
    notifyListeners();
    try {
      final userId = await _userManager.getUserId();
      final token = await _userManager.getUserToken();
      if (userId != null && token != null) {
        final updatedUser = await _userService.updateUserInfo(
          userId,
          token,
          email: email,
          phoneNumber: phoneNumber,
          dateOfBirth: dateOfBirth,
          bloodType: bloodType,
        );
        _user = updatedUser;
        await _userManager.saveUserInfo(userId, updatedUser);
        _donationCount = updatedUser.donationCount ?? 0;
      }
    } catch (e) {
      throw e;
    }
    _isLoading = false;
    notifyListeners();
  }

  Future<void> refreshAfterAppointmentCompletion(String appointmentId, {File? certificationImage}) async {
    if (!await _checkConnectivity()) {
      return;
    }

    _isLoading = true;
    notifyListeners();

    try {
      await _appointmentService.MarkcompleteAppointment(appointmentId, certificationImage: certificationImage);

      await Future.delayed(const Duration(seconds: 2));

      final userId = await _userManager.getUserId();
      final token = await _userManager.getUserToken();

      if (userId != null && token != null) {
        _user = await _userService.getUserInfo(userId, token, forceRefresh: true);
        await _userManager.updateUserProfile(_user!);

        final originalDonationCount = _donationCount;
        _donationCount = _user!.donationCount ?? 0;

        if (_donationCount == originalDonationCount && _user!.donationCount == null) {
          _donationCount = originalDonationCount + 1;

          _user = UserModel(
            id: _user!.id,
            name: _user!.name,
            email: _user!.email,
            userImage: _user!.userImage,
            bloodType: _user!.bloodType,
            job: _user!.job,
            dateOfBirth: _user!.dateOfBirth,
            donationCount: _donationCount,
            address: _user!.address,
            passportNumber: _user!.passportNumber,
            latitude: _user!.latitude,
            longitude: _user!.longitude,
            gender: _user!.gender,
            phoneNumber: _user!.phoneNumber,
          );

          await _userManager.updateUserProfile(_user!);
        }

        _daysWaiting = await _daysWaitingManager.forceRefreshDaysWaiting(userId, token);
        _appointments = await _appointmentService.getAppointments(forceRefresh: true) ?? [];
      }
    } catch (e) {
      throw e;
    }

    _isLoading = false;
    notifyListeners();
  }
}
