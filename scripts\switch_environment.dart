import 'dart:io';

/// Script để chuyển đổi môi trường một cách dễ dàng
/// Sử dụng: dart run scripts/switch_environment.dart [development|production|staging]
void main(List<String> args) {
  if (args.isEmpty) {
    print('=== CHUYỂN ĐỔI MÔI TRƯỜNG API ===');
    print('Sử dụng: dart run scripts/switch_environment.dart [environment]');
    print('');
    print('Các môi trường có sẵn:');
    print('  development  - Sử dụng local server (**************:7026)');
    print('  production   - Sử dụng Azure production server');
    print('  staging      - Sử dụng staging server');
    print('');
    print('Ví dụ:');
    print('  dart run scripts/switch_environment.dart production');
    print('  dart run scripts/switch_environment.dart development');
    return;
  }

  final environment = args[0].toLowerCase();
  final validEnvironments = ['development', 'production', 'staging'];
  
  if (!validEnvironments.contains(environment)) {
    print('❌ Môi trường không hợp lệ: $environment');
    print('<PERSON>ác môi trường hợp lệ: ${validEnvironments.join(', ')}');
    return;
  }

  try {
    switchEnvironment(environment);
    print('✅ Đã chuyển đổi thành công sang môi trường: $environment');
    print('');
    print('Các URL hiện tại:');
    showCurrentUrls(environment);
  } catch (e) {
    print('❌ Lỗi khi chuyển đổi môi trường: $e');
  }
}

void switchEnvironment(String environment) {
  final configFile = File('lib/core/config/api_config.dart');
  
  if (!configFile.existsSync()) {
    throw Exception('Không tìm thấy file cấu hình: lib/core/config/api_config.dart');
  }

  String content = configFile.readAsStringSync();
  
  // Tìm và thay thế dòng cấu hình môi trường
  final regex = RegExp(r'static const Environment _currentEnvironment = Environment\.\w+;');
  final newLine = 'static const Environment _currentEnvironment = Environment.$environment;';
  
  if (regex.hasMatch(content)) {
    content = content.replaceAll(regex, newLine);
    configFile.writeAsStringSync(content);
  } else {
    throw Exception('Không tìm thấy dòng cấu hình môi trường trong file');
  }
}

void showCurrentUrls(String environment) {
  switch (environment) {
    case 'development':
      print('  Base URL (Android Emulator): https://********:7026');
      print('  Base URL (Android Real Device): https://**************:7026');
      print('  Base URL (Web): https://localhost:7026');
      print('  API URL: [Base URL]/api');
      break;
    case 'production':
      print('  Base URL: https://bloodplus-a4a5exeadjd9anfa.southeastasia-01.azurewebsites.net');
      print('  API URL: https://bloodplus-a4a5exeadjd9anfa.southeastasia-01.azurewebsites.net/api');
      print('  Swagger: https://bloodplus-a4a5exeadjd9anfa.southeastasia-01.azurewebsites.net/swagger/index.html');
      break;
    case 'staging':
      print('  Base URL: https://**************:7026 (same as development real device)');
      print('  API URL: https://**************:7026/api');
      break;
  }
}
