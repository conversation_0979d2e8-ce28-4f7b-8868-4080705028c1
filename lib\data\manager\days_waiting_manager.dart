import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:bloodplus/data/services/user_service.dart';

class DaysWaitingManager {
  static const String _daysWaitingLastUpdateKey = 'daysWaitingLastUpdate';
  static const String _daysWaitingDataKey = 'daysWaitingData';
  static const String _lastUserIdKey = 'lastUserIdForDaysWaiting';

  Future<Map<String, int>?> getDaysWaiting(String userId, String token) async {
    final prefs = await SharedPreferences.getInstance();
    final lastUpdate = prefs.getString(_daysWaitingLastUpdateKey);
    final savedDaysWaiting = prefs.getString(_daysWaitingDataKey);
    final lastUserId = prefs.getString(_lastUserIdKey);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    // Kiểm tra nếu dữ liệu cache hợp lệ (cùng userId và cùng ngày)
    if (lastUserId == userId && lastUpdate != null && savedDaysWaiting != null) {
      final lastUpdateDate = DateTime.parse(lastUpdate);
      if (lastUpdateDate.year == today.year &&
          lastUpdateDate.month == today.month &&
          lastUpdateDate.day == today.day) {
        return Map<String, int>.from(jsonDecode(savedDaysWaiting));
      }
    }

    // Lấy dữ liệu mới từ API nếu cache không hợp lệ
    final userService = UserService();
    try {
      final daysWaiting = await userService.getDaysWaiting(userId, token);
      // Lưu dữ liệu vào SharedPreferences
      await prefs.setString(_daysWaitingLastUpdateKey, today.toIso8601String());
      await prefs.setString(_daysWaitingDataKey, jsonEncode(daysWaiting));
      await prefs.setString(_lastUserIdKey, userId);
      return daysWaiting;
    } catch (e) {
      throw Exception('Lỗi khi lấy ngày chờ: $e');
    }
  }

  Future<Map<String, int>?> forceRefreshDaysWaiting(String userId, String token) async {
    final prefs = await SharedPreferences.getInstance();
    final userService = UserService();
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    try {
      // Gọi API để lấy dữ liệu mới
      final daysWaiting = await userService.getDaysWaiting(userId, token);
      // Cập nhật cache
      await prefs.setString(_daysWaitingLastUpdateKey, today.toIso8601String());
      await prefs.setString(_daysWaitingDataKey, jsonEncode(daysWaiting));
      await prefs.setString(_lastUserIdKey, userId);
      return daysWaiting;
    } catch (e) {
      throw Exception('Lỗi khi làm mới ngày chờ: $e');
    }
  }

  Future<void> clearDaysWaiting() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_daysWaitingLastUpdateKey);
    await prefs.remove(_daysWaitingDataKey);
    await prefs.remove(_lastUserIdKey);
  }
}